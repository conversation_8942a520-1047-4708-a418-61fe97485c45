import type { Metadata } from 'next'
import './globals.css'
import AIChatWidget from './components/AIChatWidget'
import FloatingNavbar from './components/FloatingNavbar'
import DynamicBackground from './components/DynamicBackground'

export const metadata: Metadata = {
  title: '<PERSON><PERSON><PERSON><PERSON> Aalam - AI/ML Engineer Portfolio',
  description: 'Professional portfolio showcasing AI/ML projects and expertise',
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en">
      <body className="text-white relative min-h-screen">
        <DynamicBackground />
        <FloatingNavbar />
        {children}
        <AIChatWidget />
      </body>
    </html>
  )
}
